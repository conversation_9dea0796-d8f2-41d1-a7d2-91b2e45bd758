'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import AccountContextMenu from './AccountContextMenu'

interface Account {
  id: string
  name: string
  categoryId: string
  description?: string
}

interface AccountTreeItemProps {
  account: Account
  level: number
  onDataChange: () => void
}

export default function AccountTreeItem({
  account,
  level,
  onDataChange
}: AccountTreeItemProps) {
  const pathname = usePathname()
  const [showContextMenu, setShowContextMenu] = useState(false)
  const [balance, setBalance] = useState<number | null>(null)
  const [currencySymbol, setCurrencySymbol] = useState('¥')
  const isActive = pathname === `/accounts/${account.id}`

  // 获取账户余额
  useEffect(() => {
    const fetchBalance = async () => {
      try {
        const response = await fetch(`/api/accounts/${account.id}/details`)
        if (response.ok) {
          const result = await response.json()
          const accountData = result.data

          // 计算余额
          if (accountData.balances && Object.keys(accountData.balances).length > 0) {
            // 优先显示 CNY，如果没有则显示第一个货币
            const cnyBalance = accountData.balances['CNY']
            if (cnyBalance !== undefined) {
              setBalance(cnyBalance.amount)
              setCurrencySymbol(cnyBalance.currency.symbol)
            } else {
              const firstCurrency = Object.keys(accountData.balances)[0]
              const firstBalance = accountData.balances[firstCurrency]
              setBalance(firstBalance.amount)
              setCurrencySymbol(firstBalance.currency.symbol)
            }
          }
        }
      } catch (error) {
        console.error('Error fetching account balance:', error)
      }
    }

    fetchBalance()
  }, [account.id])

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault()
    setShowContextMenu(true)
  }

  const handleMenuAction = (action: string) => {
    setShowContextMenu(false)
    // 这里处理各种菜单操作
    console.log(`Action: ${action} for account: ${account.name}`)
    // 实际实现中会打开相应的模态框
  }

  return (
    <div className="relative">
      <div 
        className={`
          flex items-center group hover:bg-gray-100 rounded-md transition-colors
          ${isActive ? 'bg-blue-50 border border-blue-200' : ''}
        `}
        style={{ paddingLeft: `${level * 16 + 24}px` }}
      >
        {/* 账户图标 */}
        <div className="mr-2 flex-shrink-0">
          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>

        {/* 账户名称和余额 */}
        <div className="flex-1 py-2">
          <Link
            href={`/accounts/${account.id}`}
            className={`
              text-sm truncate block
              ${isActive ? 'text-blue-700 font-medium' : 'text-gray-600 hover:text-gray-900'}
            `}
            title={account.description || account.name}
          >
            {account.name}
          </Link>
          {balance !== null && (
            <div className={`text-xs mt-1 ${
              balance >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {currencySymbol}{Math.abs(balance).toFixed(2)}
            </div>
          )}
        </div>

        {/* 更多操作按钮 */}
        <button
          onClick={(e) => {
            e.preventDefault()
            setShowContextMenu(true)
          }}
          onContextMenu={handleContextMenu}
          className="mr-2 p-1 rounded hover:bg-gray-200 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
          </svg>
        </button>
      </div>

      {/* 上下文菜单 */}
      <AccountContextMenu
        isOpen={showContextMenu}
        onClose={() => setShowContextMenu(false)}
        onAction={handleMenuAction}
        account={account}
      />
    </div>
  )
}

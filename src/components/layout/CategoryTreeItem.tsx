'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import CategoryContextMenu from './CategoryContextMenu'

interface Category {
  id: string
  name: string
  parentId: string | null
}

interface CategoryTreeItemProps {
  category: Category
  level: number
  isExpanded: boolean
  hasChildren: boolean
  onToggle: () => void
  onDataChange: () => void
}

export default function CategoryTreeItem({
  category,
  level,
  isExpanded,
  hasChildren,
  onToggle,
  onDataChange
}: CategoryTreeItemProps) {
  const pathname = usePathname()
  const [showContextMenu, setShowContextMenu] = useState(false)
  const [balance, setBalance] = useState<number | null>(null)
  const [currencySymbol, setCurrencySymbol] = useState('¥')
  const isActive = pathname === `/categories/${category.id}`

  // 获取分类余额
  useEffect(() => {
    const fetchBalance = async () => {
      try {
        const response = await fetch(`/api/categories/${category.id}/summary`)
        if (response.ok) {
          const result = await response.json()
          const summaryData = result.data

          // 计算总余额（从账户汇总和交易汇总中计算）
          let totalBalance = 0
          let foundCurrency = false

          // 从账户余额计算
          if (summaryData.accounts && summaryData.accounts.length > 0) {
            summaryData.accounts.forEach((account: any) => {
              if (account.balances && account.balances['CNY']) {
                totalBalance += account.balances['CNY']
                foundCurrency = true
              }
            })
          }

          // 如果没有账户余额，从交易汇总计算
          if (!foundCurrency && summaryData.transactionSummary && summaryData.transactionSummary['CNY']) {
            totalBalance = summaryData.transactionSummary['CNY'].net
            foundCurrency = true
          }

          if (foundCurrency) {
            setBalance(totalBalance)
            setCurrencySymbol('¥')
          }
        }
      } catch (error) {
        console.error('Error fetching category balance:', error)
      }
    }

    fetchBalance()
  }, [category.id])

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault()
    setShowContextMenu(true)
  }

  const handleMenuAction = (action: string) => {
    setShowContextMenu(false)
    // 这里处理各种菜单操作
    console.log(`Action: ${action} for category: ${category.name}`)
    // 实际实现中会打开相应的模态框
  }

  return (
    <div className="relative">
      <div 
        className={`
          flex items-center group hover:bg-gray-100 rounded-md transition-colors
          ${isActive ? 'bg-blue-50 border border-blue-200' : ''}
        `}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
      >
        {/* 展开/折叠图标 */}
        <button
          onClick={onToggle}
          className={`
            mr-1 p-1 rounded hover:bg-gray-200 transition-colors
            ${hasChildren ? 'visible' : 'invisible'}
          `}
        >
          <svg 
            className={`h-3 w-3 text-gray-500 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>

        {/* 分类图标 */}
        <div className="mr-2 flex-shrink-0">
          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          </svg>
        </div>

        {/* 分类名称和余额 */}
        <div className="flex-1 py-2">
          <Link
            href={`/categories/${category.id}`}
            className={`
              text-sm font-medium truncate block
              ${isActive ? 'text-blue-700' : 'text-gray-700 hover:text-gray-900'}
            `}
          >
            {category.name}
          </Link>
          {balance !== null && (
            <div className={`text-xs mt-1 ${
              balance >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {currencySymbol}{Math.abs(balance).toFixed(2)}
            </div>
          )}
        </div>

        {/* 更多操作按钮 */}
        <button
          onClick={(e) => {
            e.preventDefault()
            setShowContextMenu(true)
          }}
          onContextMenu={handleContextMenu}
          className="mr-2 p-1 rounded hover:bg-gray-200 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
          </svg>
        </button>
      </div>

      {/* 上下文菜单 */}
      <CategoryContextMenu
        isOpen={showContextMenu}
        onClose={() => setShowContextMenu(false)}
        onAction={handleMenuAction}
        category={category}
      />
    </div>
  )
}
